package mcp

import (
	"context"
	"errors"
	"fmt"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/integral"
	"github.com/flipped-aurora/gin-vue-admin/server/model/mcp"
	"github.com/flipped-aurora/gin-vue-admin/server/model/mcp/request"
	"github.com/flipped-aurora/gin-vue-admin/server/model/system"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

type ProjectToolsService struct{}

// CreateProjectTools 创建projectTools表记录
// Author [yourname](https://github.com/yourname)
func (projectToolsService *ProjectToolsService) CreateProjectTools(ctx context.Context, projectTools *mcp.ProjectTools) (err error) {
	err = global.GVA_DB.Create(projectTools).Error
	return err
}

// DeleteProjectTools 删除projectTools表记录
// Author [yourname](https://github.com/yourname)
func (projectToolsService *ProjectToolsService) DeleteProjectTools(ctx context.Context, ID string) (err error) {
	err = global.GVA_DB.Delete(&mcp.ProjectTools{}, "id = ?", ID).Error
	return err
}

// DeleteProjectToolsByIds 批量删除projectTools表记录
// Author [yourname](https://github.com/yourname)
func (projectToolsService *ProjectToolsService) DeleteProjectToolsByIds(ctx context.Context, IDs []string) (err error) {
	err = global.GVA_DB.Delete(&[]mcp.ProjectTools{}, "id in ?", IDs).Error
	return err
}

// UpdateProjectTools 更新projectTools表记录
// Author [yourname](https://github.com/yourname)
func (projectToolsService *ProjectToolsService) UpdateProjectTools(ctx context.Context, projectTools mcp.ProjectTools) (err error) {
	err = global.GVA_DB.Model(&mcp.ProjectTools{}).Where("id = ?", projectTools.ID).Updates(&projectTools).Error
	return err
}

// GetProjectTools 根据ID获取projectTools表记录
// Author [yourname](https://github.com/yourname)
func (projectToolsService *ProjectToolsService) GetProjectTools(ctx context.Context, ID string) (projectTools mcp.ProjectTools, err error) {
	err = global.GVA_DB.Where("id = ?", ID).First(&projectTools).Error
	return
}

// GetProjectToolsInfoList 获取项目工具列表
func (p *ProjectToolsService) GetProjectToolsInfoList(ctx context.Context, info request.ProjectToolsSearch) (list []mcp.ProjectTools, total int64, err error) {
	db := global.GVA_DB.Model(&mcp.ProjectTools{})

	// 添加查询条件
	if info.ProjectID != nil {
		db = db.Where("project_id = ?", *info.ProjectID)
	}
	if info.Name != nil && *info.Name != "" {
		db = db.Where("name LIKE ?", "%"+*info.Name+"%")
	}
	if info.Type != nil && *info.Type != "" {
		db = db.Where("type = ?", *info.Type)
	}
	if info.Status != nil && *info.Status != "" {
		db = db.Where("status = ?", *info.Status)
	}

	err = db.Order("id desc").Find(&list).Error
	total = int64(len(list))
	return
}

// GetProjectToolsDataSource 获取项目工具数据源
func (p *ProjectToolsService) GetProjectToolsDataSource(ctx context.Context) (list []mcp.ProjectTools, err error) {
	err = global.GVA_DB.Find(&list).Error
	return
}

func (projectToolsService *ProjectToolsService) GetProjectToolsPublic(ctx context.Context) {
	// 此方法为获取数据源定义的数据
	// 请自行实现
}

// GetToolPointsByProjectIdAndName 根据projectId和name查询工具的积分Points
// Author [yourname](https://github.com/yourname)
func (projectToolsService *ProjectToolsService) GetToolPointsByProjectIdAndName(ctx context.Context, projectId int, name string) (points int, err error) {
	var projectTool mcp.ProjectTools
	err = global.GVA_DB.Where("project_id = ? AND name = ?", projectId, name).First(&projectTool).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return 0, errors.New("未找到指定的工具")
		}
		return 0, err
	}

	if projectTool.Points == nil {
		return 0, nil
	}

	return *projectTool.Points, nil
}

// UseToolWithPoints 用户调用工具并扣除积分（带事务和行锁）
func (projectToolsService *ProjectToolsService) UseToolWithPoints(ctx context.Context, userIDAny interface{}, toolID string) error {
	userID, ok := userIDAny.(uint)
	if !ok {
		return errors.New("用户ID类型错误")
	}
	return global.GVA_DB.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 1. 查询工具并加锁
		var tool mcp.ProjectTools
		if err := tx.Clauses(clause.Locking{Strength: "UPDATE"}).Where("id = ?", toolID).First(&tool).Error; err != nil {
			return errors.New("工具不存在")
		}
		if tool.Points == nil || *tool.Points <= 0 {
			// 免费工具，直接返回成功
			return nil
		}
		needPoints := *tool.Points

		// 2. 查询用户并加锁
		var user system.SysUser
		if err := tx.Clauses(clause.Locking{Strength: "UPDATE"}).Where("id = ?", userID).First(&user).Error; err != nil {
			return errors.New("用户不存在")
		}

		// 3. 检查总积分是否足够（免费积分 + 普通积分）
		totalPoints := user.FreePoints + user.Points
		if totalPoints < needPoints {
			return fmt.Errorf("用户积分不足，当前总积分：%d（免费积分：%d，普通积分：%d），所需积分：%d",
				totalPoints, user.FreePoints, user.Points, needPoints)
		}

		// 4. 优先扣除免费积分
		var freePointsUsed, regularPointsUsed int
		if user.FreePoints >= needPoints {
			// 免费积分足够
			freePointsUsed = needPoints
			regularPointsUsed = 0
		} else {
			// 先用完免费积分，再用普通积分
			freePointsUsed = user.FreePoints
			regularPointsUsed = needPoints - user.FreePoints
		}

		// 5. 更新用户积分
		if err := tx.Model(&user).Updates(map[string]interface{}{
			"free_points": user.FreePoints - freePointsUsed,
			"points":      user.Points - regularPointsUsed,
		}).Error; err != nil {
			return errors.New("积分扣除失败")
		}

		// 6. 记录积分流水
		if freePointsUsed > 0 {
			freePointsLog := integral.SysUserPoints{
				UserID:        user.ID,
				Change:        -freePointsUsed,
				Reason:        fmt.Sprintf("发动技能'%v'", *tool.Name),
				Remark:        fmt.Sprintf("调用工具[%v]（免费积分）", toolID),
				ProjectID:     uint(*tool.ProjectId),
				ProjectToolID: tool.ID,
				Type:          "mcp",
			}
			if err := tx.Create(&freePointsLog).Error; err != nil {
				return errors.New("免费积分流水记录失败")
			}
		}

		if regularPointsUsed > 0 {
			regularPointsLog := integral.SysUserPoints{
				UserID:        user.ID,
				Change:        -regularPointsUsed,
				Reason:        fmt.Sprintf("发动技能'%v'", *tool.Name),
				Remark:        fmt.Sprintf("调用工具[%v]（普通积分）", toolID),
				ProjectID:     uint(*tool.ProjectId),
				ProjectToolID: tool.ID,
				Type:          "mcp",
			}
			if err := tx.Create(&regularPointsLog).Error; err != nil {
				return errors.New("普通积分流水记录失败")
			}
		}

		return nil
	})
}
